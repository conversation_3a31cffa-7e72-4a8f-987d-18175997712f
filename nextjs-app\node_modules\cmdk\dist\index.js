"use client";var Oe=Object.create;var B=Object.defineProperty;var Ne=Object.getOwnPropertyDescriptor;var He=Object.getOwnPropertyNames;var Ge=Object.getPrototypeOf,Fe=Object.prototype.hasOwnProperty;var Ve=(e,o)=>{for(var r in o)B(e,r,{get:o[r],enumerable:!0})},fe=(e,o,r,u)=>{if(o&&typeof o=="object"||typeof o=="function")for(let i of He(o))!Fe.call(e,i)&&i!==r&&B(e,i,{get:()=>o[i],enumerable:!(u=Ne(o,i))||u.enumerable});return e};var me=(e,o,r)=>(r=e!=null?Oe(Ge(e)):{},fe(o||!e||!e.__esModule?B(r,"default",{value:e,enumerable:!0}):r,e)),Ke=e=>fe(B({},"__esModule",{value:!0}),e);var nt={};Ve(nt,{Command:()=>ze,CommandDialog:()=>xe,CommandEmpty:()=>Le,CommandGroup:()=>Te,CommandInput:()=>De,CommandItem:()=>Pe,CommandList:()=>Ie,CommandLoading:()=>_e,CommandRoot:()=>ie,CommandSeparator:()=>Me,defaultFilter:()=>Ce,useCommandState:()=>M});module.exports=Ke(nt);var x=me(require("@radix-ui/react-dialog")),n=me(require("react"));var pe=1,Be=.9,Ue=.8,je=.17,Z=.1,ee=.999,$e=.9999;var qe=.99,We=/[\\\/_+.#"@\[\(\{&]/,Xe=/[\\\/_+.#"@\[\(\{&]/g,Je=/[\s-]/,ge=/[\s-]/g;function te(e,o,r,u,i,d,f){if(d===o.length)return i===e.length?pe:qe;var p=`${i},${d}`;if(f[p]!==void 0)return f[p];for(var R=u.charAt(d),s=r.indexOf(R,i),v=0,g,b,S,T;s>=0;)g=te(e,o,r,u,s+1,d+1,f),g>v&&(s===i?g*=pe:We.test(e.charAt(s-1))?(g*=Ue,S=e.slice(i,s-1).match(Xe),S&&i>0&&(g*=Math.pow(ee,S.length))):Je.test(e.charAt(s-1))?(g*=Be,T=e.slice(i,s-1).match(ge),T&&i>0&&(g*=Math.pow(ee,T.length))):(g*=je,i>0&&(g*=Math.pow(ee,s-i))),e.charAt(s)!==o.charAt(d)&&(g*=$e)),(g<Z&&r.charAt(s-1)===u.charAt(d+1)||u.charAt(d+1)===u.charAt(d)&&r.charAt(s-1)!==u.charAt(d))&&(b=te(e,o,r,u,s+1,d+2,f),b*Z>g&&(g=b*Z)),g>v&&(v=g),s=r.indexOf(R,s+1);return f[p]=v,v}function ve(e){return e.toLowerCase().replace(ge," ")}function Re(e,o,r){return e=r&&r.length>0?`${e+" "+r.join(" ")}`:e,te(e,o,ve(e),ve(o),0,0,{})}var D=require("@radix-ui/react-primitive"),L=require("@radix-ui/react-id"),O=require("@radix-ui/react-compose-refs"),V='[cmdk-group=""]',re='[cmdk-group-items=""]',Ye='[cmdk-group-heading=""]',Ee='[cmdk-item=""]',he=`${Ee}:not([aria-disabled="true"])`,ne="cmdk-item-select",w="data-value",Ce=(e,o,r)=>Re(e,o,r),Se=n.createContext(void 0),K=()=>n.useContext(Se),ye=n.createContext(void 0),oe=()=>n.useContext(ye),be=n.createContext(void 0),ie=n.forwardRef((e,o)=>{let r=A(()=>{var t,l;return{search:"",value:(l=(t=e.value)!=null?t:e.defaultValue)!=null?l:"",selectedItemId:void 0,filtered:{count:0,items:new Map,groups:new Set}}}),u=A(()=>new Set),i=A(()=>new Map),d=A(()=>new Map),f=A(()=>new Set),p=ke(e),{label:R,children:s,value:v,onValueChange:g,filter:b,shouldFilter:S,loop:T,disablePointerSelection:Ae=!1,vimBindings:j=!0,...N}=e,$=(0,L.useId)(),q=(0,L.useId)(),H=(0,L.useId)(),I=n.useRef(null),h=et();_(()=>{if(v!==void 0){let t=v.trim();r.current.value=t,y.emit()}},[v]),_(()=>{h(6,ce)},[]);let y=n.useMemo(()=>({subscribe:t=>(f.current.add(t),()=>f.current.delete(t)),snapshot:()=>r.current,setState:(t,l,c)=>{var a,m,E,P;if(!Object.is(r.current[t],l)){if(r.current[t]=l,t==="search")Y(),X(),h(1,J);else if(t==="value"){if(document.activeElement.hasAttribute("cmdk-input")||document.activeElement.hasAttribute("cmdk-root")){let C=document.getElementById(H);C?C.focus():(a=document.getElementById($))==null||a.focus()}if(h(7,()=>{var C;r.current.selectedItemId=(C=k())==null?void 0:C.id,y.emit()}),c||h(5,ce),((m=p.current)==null?void 0:m.value)!==void 0){let C=l!=null?l:"";(P=(E=p.current).onValueChange)==null||P.call(E,C);return}}y.emit()}},emit:()=>{f.current.forEach(t=>t())}}),[]),W=n.useMemo(()=>({value:(t,l,c)=>{var a;l!==((a=d.current.get(t))==null?void 0:a.value)&&(d.current.set(t,{value:l,keywords:c}),r.current.filtered.items.set(t,ae(l,c)),h(2,()=>{X(),y.emit()}))},item:(t,l)=>(u.current.add(t),l&&(i.current.has(l)?i.current.get(l).add(t):i.current.set(l,new Set([t]))),h(3,()=>{Y(),X(),r.current.value||J(),y.emit()}),()=>{d.current.delete(t),u.current.delete(t),r.current.filtered.items.delete(t);let c=k();h(4,()=>{Y(),(c==null?void 0:c.getAttribute("id"))===t&&J(),y.emit()})}),group:t=>(i.current.has(t)||i.current.set(t,new Set),()=>{d.current.delete(t),i.current.delete(t)}),filter:()=>p.current.shouldFilter,label:R||e["aria-label"],getDisablePointerSelection:()=>p.current.disablePointerSelection,listId:$,inputId:H,labelId:q,listInnerRef:I}),[]);function ae(t,l){var a,m;let c=(m=(a=p.current)==null?void 0:a.filter)!=null?m:Ce;return t?c(t,r.current.search,l):0}function X(){if(!r.current.search||p.current.shouldFilter===!1)return;let t=r.current.filtered.items,l=[];r.current.filtered.groups.forEach(a=>{let m=i.current.get(a),E=0;m.forEach(P=>{let C=t.get(P);E=Math.max(C,E)}),l.push([a,E])});let c=I.current;G().sort((a,m)=>{var C,F;let E=a.getAttribute("id"),P=m.getAttribute("id");return((C=t.get(P))!=null?C:0)-((F=t.get(E))!=null?F:0)}).forEach(a=>{let m=a.closest(re);m?m.appendChild(a.parentElement===m?a:a.closest(`${re} > *`)):c.appendChild(a.parentElement===c?a:a.closest(`${re} > *`))}),l.sort((a,m)=>m[1]-a[1]).forEach(a=>{var E;let m=(E=I.current)==null?void 0:E.querySelector(`${V}[${w}="${encodeURIComponent(a[0])}"]`);m==null||m.parentElement.appendChild(m)})}function J(){let t=G().find(c=>c.getAttribute("aria-disabled")!=="true"),l=t==null?void 0:t.getAttribute(w);y.setState("value",l||void 0)}function Y(){var l,c,a,m;if(!r.current.search||p.current.shouldFilter===!1){r.current.filtered.count=u.current.size;return}r.current.filtered.groups=new Set;let t=0;for(let E of u.current){let P=(c=(l=d.current.get(E))==null?void 0:l.value)!=null?c:"",C=(m=(a=d.current.get(E))==null?void 0:a.keywords)!=null?m:[],F=ae(P,C);r.current.filtered.items.set(E,F),F>0&&t++}for(let[E,P]of i.current)for(let C of P)if(r.current.filtered.items.get(C)>0){r.current.filtered.groups.add(E);break}r.current.filtered.count=t}function ce(){var l,c,a;let t=k();t&&(((l=t.parentElement)==null?void 0:l.firstChild)===t&&((a=(c=t.closest(V))==null?void 0:c.querySelector(Ye))==null||a.scrollIntoView({block:"nearest"})),t.scrollIntoView({block:"nearest"}))}function k(){var t;return(t=I.current)==null?void 0:t.querySelector(`${Ee}[aria-selected="true"]`)}function G(){var t;return Array.from(((t=I.current)==null?void 0:t.querySelectorAll(he))||[])}function z(t){let c=G()[t];c&&y.setState("value",c.getAttribute(w))}function Q(t){var E;let l=k(),c=G(),a=c.findIndex(P=>P===l),m=c[a+t];(E=p.current)!=null&&E.loop&&(m=a+t<0?c[c.length-1]:a+t===c.length?c[0]:c[a+t]),m&&y.setState("value",m.getAttribute(w))}function se(t){let l=k(),c=l==null?void 0:l.closest(V),a;for(;c&&!a;)c=t>0?Qe(c,V):Ze(c,V),a=c==null?void 0:c.querySelector(he);a?y.setState("value",a.getAttribute(w)):Q(t)}let le=()=>z(G().length-1),ue=t=>{t.preventDefault(),t.metaKey?le():t.altKey?se(1):Q(1)},de=t=>{t.preventDefault(),t.metaKey?z(0):t.altKey?se(-1):Q(-1)};return n.createElement(D.Primitive.div,{ref:o,tabIndex:-1,...N,"cmdk-root":"",onKeyDown:t=>{var c;(c=N.onKeyDown)==null||c.call(N,t);let l=t.nativeEvent.isComposing||t.keyCode===229;if(!(t.defaultPrevented||l))switch(t.key){case"n":case"j":{j&&t.ctrlKey&&ue(t);break}case"ArrowDown":{ue(t);break}case"p":case"k":{j&&t.ctrlKey&&de(t);break}case"ArrowUp":{de(t);break}case"Home":{t.preventDefault(),z(0);break}case"End":{t.preventDefault(),le();break}case"Enter":{t.preventDefault();let a=k();if(a){let m=new Event(ne);a.dispatchEvent(m)}}}}},n.createElement("label",{"cmdk-label":"",htmlFor:W.inputId,id:W.labelId,style:rt},R),U(e,t=>n.createElement(ye.Provider,{value:y},n.createElement(Se.Provider,{value:W},t))))}),Pe=n.forwardRef((e,o)=>{var H,I;let r=(0,L.useId)(),u=n.useRef(null),i=n.useContext(be),d=K(),f=ke(e),p=(I=(H=f.current)==null?void 0:H.forceMount)!=null?I:i==null?void 0:i.forceMount;_(()=>{if(!p)return d.item(r,i==null?void 0:i.id)},[p]);let R=we(r,u,[e.value,e.children,u],e.keywords),s=oe(),v=M(h=>h.value&&h.value===R.current),g=M(h=>p||d.filter()===!1?!0:h.search?h.filtered.items.get(r)>0:!0);n.useEffect(()=>{let h=u.current;if(!(!h||e.disabled))return h.addEventListener(ne,b),()=>h.removeEventListener(ne,b)},[g,e.onSelect,e.disabled]);function b(){var h,y;S(),(y=(h=f.current).onSelect)==null||y.call(h,R.current)}function S(){s.setState("value",R.current,!0)}if(!g)return null;let{disabled:T,value:Ae,onSelect:j,forceMount:N,keywords:$,...q}=e;return n.createElement(D.Primitive.div,{ref:(0,O.composeRefs)(u,o),...q,id:r,"cmdk-item":"",role:"option","aria-disabled":!!T,"aria-selected":!!v,"data-disabled":!!T,"data-selected":!!v,onPointerMove:T||d.getDisablePointerSelection()?void 0:S,onClick:T?void 0:b},e.children)}),Te=n.forwardRef((e,o)=>{let{heading:r,children:u,forceMount:i,...d}=e,f=(0,L.useId)(),p=n.useRef(null),R=n.useRef(null),s=(0,L.useId)(),v=K(),g=M(S=>i||v.filter()===!1?!0:S.search?S.filtered.groups.has(f):!0);_(()=>v.group(f),[]),we(f,p,[e.value,e.heading,R]);let b=n.useMemo(()=>({id:f,forceMount:i}),[i]);return n.createElement(D.Primitive.div,{ref:(0,O.composeRefs)(p,o),...d,"cmdk-group":"",role:"presentation",hidden:g?void 0:!0},r&&n.createElement("div",{ref:R,"cmdk-group-heading":"","aria-hidden":!0,id:s},r),U(e,S=>n.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":r?s:void 0},n.createElement(be.Provider,{value:b},S))))}),Me=n.forwardRef((e,o)=>{let{alwaysRender:r,...u}=e,i=n.useRef(null),d=M(f=>!f.search);return!r&&!d?null:n.createElement(D.Primitive.div,{ref:(0,O.composeRefs)(i,o),...u,"cmdk-separator":"",role:"separator"})}),De=n.forwardRef((e,o)=>{let{onValueChange:r,...u}=e,i=e.value!=null,d=oe(),f=M(s=>s.search),p=M(s=>s.selectedItemId),R=K();return n.useEffect(()=>{e.value!=null&&d.setState("search",e.value)},[e.value]),n.createElement(D.Primitive.input,{ref:o,...u,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":R.listId,"aria-labelledby":R.labelId,"aria-activedescendant":p,id:R.inputId,type:"text",value:i?e.value:f,onChange:s=>{i||d.setState("search",s.target.value),r==null||r(s.target.value)}})}),Ie=n.forwardRef((e,o)=>{let{children:r,label:u="Suggestions",...i}=e,d=n.useRef(null),f=n.useRef(null),p=M(s=>s.selectedItemId),R=K();return n.useEffect(()=>{if(f.current&&d.current){let s=f.current,v=d.current,g,b=new ResizeObserver(()=>{g=requestAnimationFrame(()=>{let S=s.offsetHeight;v.style.setProperty("--cmdk-list-height",S.toFixed(1)+"px")})});return b.observe(s),()=>{cancelAnimationFrame(g),b.unobserve(s)}}},[]),n.createElement(D.Primitive.div,{ref:(0,O.composeRefs)(d,o),...i,"cmdk-list":"",role:"listbox",tabIndex:-1,"aria-activedescendant":p,"aria-label":u,id:R.listId},U(e,s=>n.createElement("div",{ref:(0,O.composeRefs)(f,R.listInnerRef),"cmdk-list-sizer":""},s)))}),xe=n.forwardRef((e,o)=>{let{open:r,onOpenChange:u,overlayClassName:i,contentClassName:d,container:f,...p}=e;return n.createElement(x.Root,{open:r,onOpenChange:u},n.createElement(x.Portal,{container:f},n.createElement(x.Overlay,{"cmdk-overlay":"",className:i}),n.createElement(x.Content,{"aria-label":e.label,"cmdk-dialog":"",className:d},n.createElement(ie,{ref:o,...p}))))}),Le=n.forwardRef((e,o)=>M(u=>u.filtered.count===0)?n.createElement(D.Primitive.div,{ref:o,...e,"cmdk-empty":"",role:"presentation"}):null),_e=n.forwardRef((e,o)=>{let{progress:r,children:u,label:i="Loading...",...d}=e;return n.createElement(D.Primitive.div,{ref:o,...d,"cmdk-loading":"",role:"progressbar","aria-valuenow":r,"aria-valuemin":0,"aria-valuemax":100,"aria-label":i},U(e,f=>n.createElement("div",{"aria-hidden":!0},f)))}),ze=Object.assign(ie,{List:Ie,Item:Pe,Input:De,Group:Te,Separator:Me,Dialog:xe,Empty:Le,Loading:_e});function Qe(e,o){let r=e.nextElementSibling;for(;r;){if(r.matches(o))return r;r=r.nextElementSibling}}function Ze(e,o){let r=e.previousElementSibling;for(;r;){if(r.matches(o))return r;r=r.previousElementSibling}}function ke(e){let o=n.useRef(e);return _(()=>{o.current=e}),o}var _=typeof window=="undefined"?n.useEffect:n.useLayoutEffect;function A(e){let o=n.useRef();return o.current===void 0&&(o.current=e()),o}function M(e){let o=oe(),r=()=>e(o.snapshot());return n.useSyncExternalStore(o.subscribe,r,r)}function we(e,o,r,u=[]){let i=n.useRef(),d=K();return _(()=>{var R;let f=(()=>{var s;for(let v of r){if(typeof v=="string")return v.trim();if(typeof v=="object"&&"current"in v)return v.current?(s=v.current.textContent)==null?void 0:s.trim():i.current}})(),p=u.map(s=>s.trim());d.value(e,f,p),(R=o.current)==null||R.setAttribute(w,f),i.current=f}),i}var et=()=>{let[e,o]=n.useState(),r=A(()=>new Map);return _(()=>{r.current.forEach(u=>u()),r.current=new Map},[e]),(u,i)=>{r.current.set(u,i),o({})}};function tt(e){let o=e.type;return typeof o=="function"?o(e.props):"render"in o?o.render(e.props):e}function U({asChild:e,children:o},r){return e&&n.isValidElement(o)?n.cloneElement(tt(o),{ref:o.ref},r(o.props.children)):r(o)}var rt={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};0&&(module.exports={Command,CommandDialog,CommandEmpty,CommandGroup,CommandInput,CommandItem,CommandList,CommandLoading,CommandRoot,CommandSeparator,defaultFilter,useCommandState});
