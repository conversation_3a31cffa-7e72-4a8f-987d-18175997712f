import { Search, StickyNote, Brain, BookOpen, MessageSquare, Zap } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"

const features = [
  {
    icon: Search,
    title: "Intelligent Search",
    description: "Search the Quran by meaning, context, or keywords with AI-powered understanding",
    color: "text-blue-500"
  },
  {
    icon: StickyNote,
    title: "Smart Notes",
    description: "Generate personalized study notes, summaries, and insights from any verse",
    color: "text-green-500"
  },
  {
    icon: Brain,
    title: "Interactive Learning",
    description: "Create flashcards, quizzes, and games to reinforce your understanding",
    color: "text-purple-500"
  },
  {
    icon: BookOpen,
    title: "Contextual Tafsir",
    description: "Access multiple tafsir sources with contextual explanations and translations",
    color: "text-orange-500"
  },
  {
    icon: MessageSquare,
    title: "AI Chat Assistant",
    description: "Ask questions and get detailed explanations about verses and concepts",
    color: "text-pink-500"
  },
  {
    icon: Zap,
    title: "OCR & Analysis",
    description: "Upload images of text and get instant verse recognition and analysis",
    color: "text-yellow-500"
  }
]

export function FeatureCards() {
  return (
    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
      {features.map((feature) => {
        const IconComponent = feature.icon
        return (
          <Card key={feature.title} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <div className={`p-2 rounded-lg bg-muted ${feature.color}`}>
                  <IconComponent className="h-5 w-5" />
                </div>
                <span>{feature.title}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                {feature.description}
              </p>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
