"use client"

import { Badge } from "@/components/ui/badge"

interface SuggestionChipsProps {
  onChipClick: (suggestion: string) => void
}

const suggestions = [
  "Prayer times",
  "Patience in Quran",
  "Stories of prophets",
  "Gratitude verses",
  "Forgiveness",
  "Charity and giving",
  "Faith and belief",
  "Paradise description"
]

export function SuggestionChips({ onChipClick }: SuggestionChipsProps) {
  return (
    <div className="mt-4">
      <p className="text-sm text-muted-foreground mb-3 text-center">
        Popular searches:
      </p>
      <div className="flex flex-wrap justify-center gap-2">
        {suggestions.map((suggestion) => (
          <Badge
            key={suggestion}
            variant="outline"
            onClick={() => onChipClick(suggestion)}
            className="cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
          >
            {suggestion}
          </Badge>
        ))}
      </div>
    </div>
  )
}
